import { getToken } from '@/utils/auth';
import { defHttp } from '/@/utils/http/axios';

export interface CustomRequestVo {
  id: number;
  title: string;
  route: string;
  icon: string;
  bgColor?: string;
}

export interface CustomRequestQo {
  title: string;
  route: string;
  icon: string;
  color: string;
}

export interface CustomRequestResponse {
  code: string;
  message: string;
  data: CustomRequestVo[];
}

export interface BaseResponse {
  code: string;
  message: string;
}

export interface WarningInfoVo {
  tsxx: string;
  bjnumber: number;
  gnbh: string;
}

export interface WarningDetailVo {
  dspcode: string;
  dspname: string;
  shengccj: string;
  dspspgg: string;
  jldw: string;
  kcsl: number;
  miejph: string;
  sxrq: string;
  picih: string;
  dqts: number;
}

export interface WarningInfoResponse {
  code: string;
  message: string;
  data: {
    tableheader: {
      tsxx: string;
      bjnumber: string;
      gnbh: string;
    };
    tablevalue: WarningInfoVo[];
  };
}

export interface WarningDetailResponse {
  code: string;
  message: string;
  data:
    | WarningDetailVo[]
    | {
        tableheader: { [key: string]: string };
        tablevalue: WarningDetailVo[];
      };
}

export enum Api {
  GetCustomRequestList = '/system/getCustomRequestList',
  AddCustomRequests = '/system/addCustomRequests',
  DeleteCustomRequests = '/system/deleteCustomRequests',
  // ExportPdf = '/fastReport/export/pdf',
  ExportSvgZip = '/fastReport/export/pdf',
  GetPrintSchemes = '/fastReport/getRptFa',
  GetWarningInfo = '/system/warning/info',
  GetWarningDetail = '/system/warning/detail',
}

/**
 * 获取自定义请求列表
 * @returns 自定义请求列表数据
 */
export function getCustomRequestList() {
  return defHttp.get<CustomRequestResponse>({
    url: Api.GetCustomRequestList,
  });
}

/**
 * 批量添加自定义请求
 * @param datas - 自定义请求数据列表
 * @returns 操作结果
 */
export function addCustomRequests(datas: CustomRequestQo[]) {
  return defHttp.post<BaseResponse>({
    url: Api.AddCustomRequests,
    data: [...datas],
  });
}

/**
 * 批量删除自定义请求
 * @param datas - 自定义请求ID列表
 * @returns 操作结果
 */
export function deleteCustomRequests(datas: number[]) {
  return defHttp.post<BaseResponse>({
    url: Api.DeleteCustomRequests,
    data: [...datas],
  });
}

/**
 * 导出PDF文件
 * @param djlx 单据类型
 * @param danjbh 单据编号
 * @returns Blob
 */
export async function exportPdf(djlx: string, danjbh: string): Promise<Blob> {
  const url = `/basic-api/fastReport/export/pdf?djlx=${djlx}&danjbh=${danjbh}`;
  const token = getToken();

  if (!token) {
    throw new Error('未找到认证令牌，请重新登录');
  }

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'x-auth-token': token,
    },
  });

  if (!response.ok) {
    throw new Error(`导出失败: ${response.statusText}`);
  }

  return await response.blob();
}

/**
 * 获取系统预警信息
 * @returns 系统预警信息列表
 */
export function getWarningInfo() {
  return defHttp.get<WarningInfoResponse>({
    url: Api.GetWarningInfo,
  });
}

/**
 * 获取系统预警信息详情
 * @param gnbh 功能编号
 * @returns 系统预警信息详情
 */
export function getWarningDetail(gnbh: string) {
  return defHttp.get<WarningDetailResponse>({
    url: `${Api.GetWarningDetail}?gnbh=${gnbh}`,
  });
}

/**
 * 打印方案的返回数据接口
 */
export interface PrintSchemeVo {
  dataid: string;
  faid: string;
  faname: string;
  orgName: string;
  orgNo: string;
}

/**
 * 打印方案响应接口
 */
export interface PrintSchemeResponse {
  code: string;
  message: string;
  data: PrintSchemeVo[];
}

/**
 * SVG导出ZIP文件的请求参数接口
 */
export interface ExportSvgZipParams {
  djlx: string; // 单据类型
  type: string; // 业务类型（0-单据，1-查询，2-其它）
  dataid: string; // 方案编号（type为0时传djlx，type为1时传quaryid）
  faid: string; // 方案id
  paramMap?: Record<string, any>; // 参数和对应值集合
}

/**
 * 获取打印方案列表
 * @param djlx 单据类型
 * @param type 业务类型，默认为 "0"（表示单据类型）
 * @param dataid 方案编号，默认使用 djlx 的值
 * @returns 打印方案列表
 */
export function getPrintSchemes(type: string = '0', dataid?: string) {
  const finalDataid = dataid;
  return defHttp.get<PrintSchemeResponse>({
    url: `${Api.GetPrintSchemes}?&type=${type}&dataid=${finalDataid}`,
  });
}

/**
 * 导出SVG格式的ZIP压缩文件
 * @param params 导出参数
 * @returns Blob
 */
export async function exportSvgZip(params: ExportSvgZipParams): Promise<Blob> {
  const url = `/basic-api${Api.ExportSvgZip}`;
  const token = getToken();

  if (!token) {
    throw new Error('未找到认证令牌，请重新登录');
  }

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-auth-token': token,
    },
    body: JSON.stringify(params),
  });

  if (!response.ok) {
    throw new Error(`导出失败: ${response.statusText}`);
  }

  return await response.blob();
}
